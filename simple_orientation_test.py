#!/usr/bin/env python3
"""
Simple test for the improved orientation detection
"""

import cv2
import numpy as np

def detect_text_orientation_opencv(img):
    """Detect text orientation using OpenCV text detection and line analysis"""
    try:
        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Apply binary threshold
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # Find contours for text regions
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return 0
        
        # Filter contours by area to get text-like regions
        text_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 50 < area < 10000:  # Filter for text-sized regions
                text_contours.append(contour)
        
        if not text_contours:
            return 0
        
        # Calculate orientation angles for text regions
        angles = []
        for contour in text_contours:
            # Get minimum area rectangle
            rect = cv2.minAreaRect(contour)
            angle = rect[2]
            
            # Normalize angle to [-90, 0] range
            if angle > -45:
                angle = angle - 90
            
            angles.append(angle)
        
        if not angles:
            return 0
        
        # Use median angle to avoid outliers
        median_angle = np.median(angles)
        
        print(f"Detected angles: {angles[:10]}...")  # Show first 10 angles
        print(f"Median angle: {median_angle}")
        
        # Round to nearest increment for rotations (IMPROVED)
        if abs(median_angle) > 75:
            return -90 if median_angle < 0 else 90
        elif abs(median_angle) > 60:
            return -75 if median_angle < 0 else 75
        elif abs(median_angle) > 37.5:
            return -45 if median_angle < 0 else 45
        elif abs(median_angle) > 22.5:
            return -30 if median_angle < 0 else 30
        elif abs(median_angle) > 7.5:
            return -15 if median_angle < 0 else 15
        elif abs(median_angle) > 2:
            return median_angle
        else:
            return 0
            
    except Exception as e:
        print(f"Warning: OpenCV text orientation detection failed: {str(e)}")
        return 0

def check_opencv_orientation(img):
    """Improved OpenCV orientation check"""
    try:
        # Detect the rotation angle needed
        rotation_angle = detect_text_orientation_opencv(img)
        
        print(f"OpenCV detected rotation angle: {rotation_angle}")
        
        # Only rotate if the angle is significant (IMPROVED: lowered threshold)
        if abs(rotation_angle) > 1.0:
            height, width = img.shape[:2]
            center = (width // 2, height // 2)
            
            # Create rotation matrix
            rotation_matrix = cv2.getRotationMatrix2D(center, -rotation_angle, 1.0)
            
            # Calculate new bounding dimensions to avoid cropping
            abs_cos = abs(rotation_matrix[0,0])
            abs_sin = abs(rotation_matrix[0,1])
            
            new_width = int(height * abs_sin + width * abs_cos)
            new_height = int(height * abs_cos + width * abs_sin)
            
            # Adjust the rotation matrix to account for translation
            rotation_matrix[0, 2] += (new_width - width) / 2
            rotation_matrix[1, 2] += (new_height - height) / 2
            
            rotated = cv2.warpAffine(img, rotation_matrix, (new_width, new_height),
                                   flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_CONSTANT, 
                                   borderValue=(255, 255, 255))
            return rotated
            
    except Exception as e:
        print(f"Warning: OpenCV orientation check failed: {str(e)}")
    
    return img

def main():
    # Load test image
    img = cv2.imread('test_rotated_image.png')
    if img is None:
        print("Error: Could not load test_rotated_image.png")
        return
    
    print(f"Loaded image shape: {img.shape}")
    
    # Test improved OpenCV orientation
    print("\n=== Testing Improved OpenCV Orientation Detection ===")
    result = check_opencv_orientation(img.copy())
    
    # Save result
    cv2.imwrite('improved_opencv_result.png', result)
    print("Result saved as improved_opencv_result.png")
    
    # Compare with original
    print(f"Original image shape: {img.shape}")
    print(f"Corrected image shape: {result.shape}")

if __name__ == "__main__":
    main()
