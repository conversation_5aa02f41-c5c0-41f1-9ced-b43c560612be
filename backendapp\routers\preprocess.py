from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks, UploadFile, File
from sqlalchemy.orm import Session
from typing import Dict, Any, List
import os
import fitz  # PyMuPDF
import cv2
import numpy as np
from pathlib import Path
from skimage import io, filters, morphology, exposure
from skimage.util import img_as_ubyte
import json
from datetime import datetime
import traceback
import re
from concurrent.futures import ThreadPoolExecutor

from ..database import SessionLocal
from ..schema import AnswerSheet, AnswerSheetPage
from ..services.deskew import deskew_image
from ..services.binarization import sauvola_binarization
from ..services.noise_removal import remove_noise, crop_borders

# Try to import pytesseract, but make it optional
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("Warning: pytesseract not available. Orientation detection will be disabled.")

router = APIRouter(
    prefix="/preprocess",
    tags=["preprocess"]
)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def convert_numpy_types(obj):
    """Convert numpy types to native Python types for JSON serialization."""
    if isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif hasattr(obj, 'item'):  # For numpy scalars
        return obj.item()
    elif hasattr(obj, 'tolist'):  # For numpy arrays
        return obj.tolist()
    else:
        return obj

def rotate_image(image, angle):
    """Rotate image by given angle"""
    height, width = image.shape[:2]
    center = (width // 2, height // 2)
    rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
    return cv2.warpAffine(image, rotation_matrix, (width, height))

def detect_text_orientation_opencv(img):
    """Detect text orientation using OpenCV text detection and line analysis"""
    try:
        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()

        # Apply binary threshold
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Find contours for text regions
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return 0

        # Filter contours by area to get text-like regions
        text_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 50 < area < 10000:  # Filter for text-sized regions
                text_contours.append(contour)

        if not text_contours:
            return 0

        # Calculate orientation angles for text regions
        angles = []
        for contour in text_contours:
            # Get minimum area rectangle
            rect = cv2.minAreaRect(contour)
            angle = rect[2]

            # Normalize angle to [-90, 0] range
            if angle > -45:
                angle = angle - 90

            angles.append(angle)

        if not angles:
            return 0

        # Use median angle to avoid outliers
        median_angle = np.median(angles)

        # Round to nearest increment for rotations
        if abs(median_angle) > 75:
            return -90 if median_angle < 0 else 90
        elif abs(median_angle) > 60:
            return -75 if median_angle < 0 else 75
        elif abs(median_angle) > 37.5:
            return -45 if median_angle < 0 else 45
        elif abs(median_angle) > 22.5:
            return -30 if median_angle < 0 else 30
        elif abs(median_angle) > 7.5:
            return -15 if median_angle < 0 else 15
        elif abs(median_angle) > 2:
            return median_angle
        else:
            return 0

    except Exception as e:
        print(f"Warning: OpenCV text orientation detection failed: {str(e)}")
        return 0

def check_opencv_orientation(img):
    """Fallback orientation check using OpenCV with improved logic"""
    try:
        # Detect the rotation angle needed
        rotation_angle = detect_text_orientation_opencv(img)

        print(f"OpenCV detected rotation angle: {rotation_angle}")

        # Only rotate if the angle is significant (lowered threshold for better detection)
        if abs(rotation_angle) > 1.0:
            height, width = img.shape[:2]
            center = (width // 2, height // 2)

            # Create rotation matrix
            rotation_matrix = cv2.getRotationMatrix2D(center, -rotation_angle, 1.0)

            # Calculate new bounding dimensions to avoid cropping
            abs_cos = abs(rotation_matrix[0,0])
            abs_sin = abs(rotation_matrix[0,1])

            new_width = int(height * abs_sin + width * abs_cos)
            new_height = int(height * abs_cos + width * abs_sin)

            # Adjust the rotation matrix to account for translation
            rotation_matrix[0, 2] += (new_width - width) / 2
            rotation_matrix[1, 2] += (new_height - height) / 2

            rotated = cv2.warpAffine(img, rotation_matrix, (new_width, new_height),
                                   flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_CONSTANT,
                                   borderValue=(255, 255, 255))
            return rotated

    except Exception as e:
        print(f"Warning: OpenCV orientation check failed: {str(e)}")

    return img

def ensure_orientation(img):
    """Ensure image is in correct orientation with improved detection"""
    if not TESSERACT_AVAILABLE:
        print("Tesseract not available, using OpenCV fallback")
        return check_opencv_orientation(img)

    try:
        # Preprocess image for better OSD detection
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()

        # Enhance contrast for better text detection
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Get orientation and script detection with multiple attempts
        osd = None
        for config in ['--psm 0', '--psm 0 -c min_characters_to_try=5']:
            try:
                osd = pytesseract.image_to_osd(enhanced, config=config)
                break
            except:
                continue

        if not osd:
            print("Tesseract OSD failed, using OpenCV fallback")
            return check_opencv_orientation(img)

        print(f"Tesseract OSD output:\n{osd}")

        # Extract orientation angle
        angle_match = re.search(r'Orientation in degrees:\s*(\d+)', osd)
        if angle_match:
            angle = int(angle_match.group(1))

            # Extract confidence
            confidence_match = re.search(r'Orientation confidence:\s*(\d+\.?\d*)', osd)
            confidence = float(confidence_match.group(1)) if confidence_match else 0

            print(f"Detected angle: {angle}°, confidence: {confidence}")

            # Only rotate if confidence is reasonable and angle needs correction
            if confidence > 1.0 and angle != 0:
                print(f"Applying Tesseract rotation: {angle}°")
                if angle == 90:
                    rotated = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
                elif angle == 180:
                    rotated = cv2.rotate(img, cv2.ROTATE_180)
                elif angle == 270:
                    rotated = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
                else:
                    rotated = img

                return rotated
            else:
                print(f"Low confidence ({confidence}) or no rotation needed, using OpenCV fallback")
                return check_opencv_orientation(img)

    except Exception as e:
        print(f"Warning: Tesseract orientation detection failed: {str(e)}")

    # Fallback to OpenCV method
    print("Using OpenCV fallback for orientation correction")
    return check_opencv_orientation(img)

def standardize_resolution(img, target_dpi=300):
    """Standardize image resolution"""
    try:
        current_dpi = img.shape[1] / (8.5)  # Assuming standard paper width
        if current_dpi != target_dpi:
            scale_factor = target_dpi / current_dpi
            new_width = int(img.shape[1] * scale_factor)
            new_height = int(img.shape[0] * scale_factor)
            img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
    except Exception as e:
        print(f"Warning: Resolution standardization failed: {str(e)}")
    return img

def enhanced_preprocess(img):
    """Apply additional enhancements to the preprocessed image"""
    # Contrast enhancement
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(img)
    
    # Slight dilation to connect broken parts
    kernel = np.ones((2,2), np.uint8)
    enhanced = cv2.dilate(enhanced, kernel, iterations=1)
    
    return enhanced

def check_image_quality(img):
    """Check if image meets minimum quality standards"""
    quality_metrics = {}
    
    # Check resolution
    height, width = img.shape[:2]
    if height < 300 or width < 300:
        quality_metrics["resolution"] = {"passed": False, "message": "Image too small"}
    else:
        quality_metrics["resolution"] = {"passed": True, "message": "Resolution OK"}
    
    # Check contrast
    std_dev = np.std(img)
    if std_dev < 30:
        quality_metrics["contrast"] = {"passed": False, "message": "Low contrast"}
    else:
        quality_metrics["contrast"] = {"passed": True, "message": "Contrast OK"}
    
    # Check blurriness
    laplacian_var = cv2.Laplacian(img, cv2.CV_64F).var()
    if laplacian_var < 100:
        quality_metrics["sharpness"] = {"passed": False, "message": "Image too blurry"}
    else:
        quality_metrics["sharpness"] = {"passed": True, "message": "Sharpness OK"}
    
    # Overall quality assessment
    all_passed = all(metric["passed"] for metric in quality_metrics.values())
    quality_metrics["overall"] = {
        "passed": all_passed,
        "score": sum(metric["passed"] for metric in quality_metrics.values()) / len(quality_metrics)
    }
    
    return quality_metrics

def apply_enhancement_filter(img):
    """Apply additional enhancement filters for low-quality images"""
    # Denoising
    denoised = cv2.fastNlMeansDenoising(img)
    
    # Sharpening
    kernel = np.array([[-1,-1,-1],
                       [-1, 9,-1],
                       [-1,-1,-1]])
    sharpened = cv2.filter2D(denoised, -1, kernel)
    
    # Contrast enhancement
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(sharpened)
    
    return enhanced

@router.post("/answer-sheet/{answer_sheet_id}", status_code=status.HTTP_202_ACCEPTED)
async def preprocess_answer_sheet(
    answer_sheet_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Preprocess an answer sheet: convert PDF pages to images, apply image processing,
    and prepare for AI analysis.
    """
    try:
        # Get the answer sheet record
        answer_sheet = db.query(AnswerSheet).filter(AnswerSheet.id == answer_sheet_id).first()
        if not answer_sheet:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Answer sheet with ID {answer_sheet_id} not found"
            )
        
        if answer_sheet.processing_status != "uploaded":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Answer sheet is not in 'uploaded' status. Current status: {answer_sheet.processing_status}"
            )

        # Update status to processing
        answer_sheet.processing_status = "processing"
        answer_sheet.updated_at = datetime.now()
        db.commit()

        # Start background processing
        background_tasks.add_task(
            process_answer_sheet_pages,
            answer_sheet_id,
            answer_sheet.original_file_path
        )

        return {
            "message": "Preprocessing started",
            "answer_sheet_id": answer_sheet_id,
            "status": "processing"
        }

    except HTTPException:
        raise
    except Exception as e:
        # Update status to failed in case of error
        if 'answer_sheet' in locals():
            answer_sheet.processing_status = "failed"
            answer_sheet.file_metadata = answer_sheet.file_metadata or {}
            answer_sheet.file_metadata["error"] = str(e)
            answer_sheet.updated_at = datetime.now()
            db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Preprocessing failed: {str(e)}"
        )

def process_answer_sheet_pages(answer_sheet_id: int, pdf_path: str):
    """Background task to process all pages of an answer sheet"""
    db = SessionLocal()
    try:
        # Open the PDF
        pdf_document = fitz.open(pdf_path)
        total_pages = len(pdf_document)
        
        # Get the answer sheet to retrieve participant ID
        answer_sheet = db.query(AnswerSheet).filter(AnswerSheet.id == answer_sheet_id).first()
        if not answer_sheet:
            raise Exception(f"Answer sheet with ID {answer_sheet_id} not found")
        
        participant_id = str(answer_sheet.participant_id)
        
        # Create directory for processed images
        base_dir = os.path.dirname(pdf_path)
        processed_dir = os.path.join(base_dir, "processed")
        os.makedirs(processed_dir, exist_ok=True)
        
        # Initialize metadata
        preprocessing_metadata = {
            "total_pages": total_pages,
            "pages": [],
            "processing_timestamp": datetime.now().isoformat(),
            "quality_metrics": {}
        }
        
        # Process pages in batches
        batch_size = 4
        with ThreadPoolExecutor(max_workers=batch_size) as executor:
            futures = []
            for page_num in range(total_pages):
                future = executor.submit(
                    process_single_page,
                    pdf_document,
                    page_num,
                    processed_dir,
                    participant_id
                )
                futures.append(future)
            
            # Collect results
            pages_to_insert = []
            for future in futures:
                page_metadata = future.result()
                preprocessing_metadata["pages"].append(page_metadata)
                
                # Create answer_sheet_pages record
                answer_sheet_page = AnswerSheetPage(
                    answer_sheet_id=answer_sheet_id,
                    page_number=page_metadata["page_number"],
                    image_path=page_metadata["processed_image_path"],
                    page_status="preprocessed",
                    page_metadata=page_metadata
                )
                pages_to_insert.append(answer_sheet_page)
            
            # Bulk insert all pages
            db.bulk_save_objects(pages_to_insert)
        
        # Update answer sheet metadata and status
        answer_sheet.processing_status = "preprocessed"
        answer_sheet.updated_at = datetime.now()
        current_metadata = answer_sheet.file_metadata or {}
        current_metadata["preprocessing"] = preprocessing_metadata
        answer_sheet.file_metadata = current_metadata
        
        db.commit()
        pdf_document.close()
        
    except Exception as e:
        db.rollback()
        # Update status to failed
        answer_sheet = db.query(AnswerSheet).filter(AnswerSheet.id == answer_sheet_id).first()
        if answer_sheet:
            answer_sheet.processing_status = "failed"
            current_metadata = answer_sheet.file_metadata or {}
            current_metadata["error"] = str(e)
            current_metadata["traceback"] = traceback.format_exc()
            answer_sheet.file_metadata = current_metadata
            answer_sheet.updated_at = datetime.now()
            db.commit()
        raise
    finally:
        db.close()

def process_single_page(pdf_document, page_num: int, processed_dir: str, participant_id: str = None) -> Dict[str, Any]:
    """Process a single PDF page and return metadata"""
    page_metadata = {
        "page_number": page_num + 1,
        "processing_steps": [],
        "quality_metrics": {},
        "enhancement_applied": False
    }
    
    try:
        # Step 1: Convert PDF page to high-quality image (300 DPI)
        page = pdf_document.load_page(page_num)
        mat = fitz.Matrix(300/72, 300/72)  # 300 DPI
        pix = page.get_pixmap(matrix=mat)
        img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, pix.n)
        
        print(f"Page {page_num + 1}: Original size {pix.width}x{pix.height}, {pix.n} channels")
        
        page_metadata["processing_steps"].append({
            "step": "pdf_to_image",
            "dpi": 300,
            "resolution": f"{pix.width}x{pix.height}",
            "success": True
        })
        
        # Step 2: Convert to appropriate color space
        if pix.n == 4:  # RGBA
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)
        elif pix.n == 1:  # Grayscale
            img_array = cv2.cvtColor(img_array, cv2.COLOR_GRAY2RGB)
        
        # Debug: Test initial text detection
        if TESSERACT_AVAILABLE:
            try:
                test_text = pytesseract.image_to_string(img_array, config='--psm 6')
                print(f"Page {page_num + 1}: Initial text detection: '{test_text[:50]}...'")
            except Exception as e:
                print(f"Page {page_num + 1}: Initial text detection failed: {e}")
               
        # Step 3: Ensure correct orientation with fallback
        print(f"Page {page_num + 1}: Checking orientation...")
        oriented_img = ensure_orientation(img_array)
        
        # If Tesseract didn't detect orientation or failed, try OpenCV method
        if TESSERACT_AVAILABLE:
            # Check if orientation was actually applied
            original_hash = hash(img_array.tobytes())
            oriented_hash = hash(oriented_img.tobytes())
            
            if original_hash == oriented_hash:  # No rotation was applied
                print(f"Page {page_num + 1}: Tesseract didn't apply rotation, trying OpenCV...")
                oriented_img = check_opencv_orientation(img_array)
        else:
            print(f"Page {page_num + 1}: Tesseract not available, using OpenCV...")
            oriented_img = check_opencv_orientation(img_array)
        
        page_metadata["processing_steps"].append({
            "step": "orientation_correction",
            "success": True,
            "tesseract_available": TESSERACT_AVAILABLE
        })
        
        # Step 4: Standardize resolution
        resized_img = standardize_resolution(oriented_img)
        page_metadata["processing_steps"].append({
            "step": "resolution_standardization",
            "success": True
        })
        
        # Step 5: Deskew the image (only for small skew corrections)
        # Use more conservative parameters to avoid over-correction
        deskewed_img, skew_angle = deskew_image(
            resized_img,
            hough_threshold=150,  # Higher threshold for more selective line detection
            max_skew_angle=15.0   # Limit to small skew corrections only
        )
        page_metadata["quality_metrics"]["initial_skew_angle"] = skew_angle
        page_metadata["processing_steps"].append({
            "step": "deskew",
            "skew_angle": skew_angle,
            "success": True
        })
        
        # Step 6: Convert to Grayscale
        gray_img = cv2.cvtColor(deskewed_img, cv2.COLOR_RGB2GRAY)
        page_metadata["processing_steps"].append({
            "step": "grayscale",
            "success": True
        })
        
        # Step 7: Apply Binarization (Sauvola's method)
        binary_img = sauvola_binarization(gray_img)
        page_metadata["processing_steps"].append({
            "step": "binarization",
            "method": "sauvola",
            "success": True
        })
        
        # Step 8: Remove noise and crop borders
        cleaned_img = remove_noise(binary_img)
        cropped_img, crop_metrics = crop_borders(cleaned_img)
        
        page_metadata["quality_metrics"].update(crop_metrics)
        page_metadata["processing_steps"].append({
            "step": "noise_removal_cropping",
            "success": True
        })
        
        # Step 9: Apply enhanced preprocessing
        enhanced_img = enhanced_preprocess(cropped_img)
        
        # Step 10: Check image quality
        quality_metrics = check_image_quality(enhanced_img)
        page_metadata["quality_metrics"].update(quality_metrics)
        
        # Apply additional enhancement if quality is poor
        if not quality_metrics["overall"]["passed"]:
            enhanced_img = apply_enhancement_filter(enhanced_img)
            page_metadata["enhancement_applied"] = True
            page_metadata["processing_steps"].append({
                "step": "additional_enhancement",
                "reason": "poor_quality",
                "success": True
            })
        
        # Save the processed image with participant ID in the path
        if participant_id:
            participant_processed_dir = os.path.join(processed_dir, participant_id)
            os.makedirs(participant_processed_dir, exist_ok=True)
            processed_filename = f"page_{page_num + 1}_processed.png"
            processed_image_path = os.path.join(participant_processed_dir, processed_filename)
        else:
            processed_filename = f"page_{page_num + 1}_processed.png"
            processed_image_path = os.path.join(processed_dir, processed_filename)
            
        cv2.imwrite(processed_image_path, enhanced_img)
        page_metadata["processed_image_path"] = processed_image_path
        
        # Calculate overall quality score
        page_metadata["quality_metrics"]["overall_score"] = calculate_quality_score(page_metadata)
        
        # Convert all numpy types in the metadata
        page_metadata = convert_numpy_types(page_metadata)
        
        print(f"Page {page_num + 1}: Processing completed successfully")
        return page_metadata
        
    except Exception as e:
        page_metadata["processing_steps"].append({
            "step": "error",
            "error": str(e),
            "success": False
        })
        print(f"Page {page_num + 1}: Processing failed: {e}")
        raise

def calculate_quality_score(metadata: Dict[str, Any]) -> float:
    """Calculate an overall quality score based on processing metrics"""
    score = 100.0  # Start with perfect score
    
    # Deduct points for skew
    skew_angle = abs(metadata["quality_metrics"].get("initial_skew_angle", 0))
    if skew_angle > 5:
        score -= min(30, skew_angle * 2)
    
    # Deduct points for excessive cropping
    crop_ratio = metadata["quality_metrics"].get("crop_ratio", 1.0)
    if crop_ratio < 0.8:  # More than 20% cropped
        score -= (1 - crop_ratio) * 50
    
    # Include quality check results
    quality_metrics = metadata["quality_metrics"]
    if "overall" in quality_metrics:
        score *= quality_metrics["overall"]["score"]
    
    return max(0, min(100, score))

@router.post("/test-orientation")
async def test_orientation(file: UploadFile = File(...)):
    """Test orientation detection on a single image"""
    contents = await file.read()
    nparr = np.frombuffer(contents, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

    print(f"Original image shape: {img.shape}")

    # Test OpenCV method first
    print("=== Testing OpenCV orientation detection ===")
    opencv_result = check_opencv_orientation(img.copy())

    # Test Tesseract method
    print("=== Testing Tesseract orientation detection ===")
    tesseract_result = ensure_orientation(img.copy())

    # Test the combined pipeline
    print("=== Testing full preprocessing pipeline ===")

    # Save results for comparison
    cv2.imwrite("original.png", img)
    cv2.imwrite("tesseract_corrected.png", tesseract_result)
    cv2.imwrite("opencv_corrected.png", opencv_result)
    
    return {
        "original_size": img.shape,
        "tesseract_size": tesseract_result.shape,
        "opencv_size": opencv_result.shape
    }