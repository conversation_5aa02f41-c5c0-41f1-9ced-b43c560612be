#!/usr/bin/env python3
"""
Test script for debugging orientation detection issues.
Run this script to test orientation detection on your problematic image.
"""

import cv2
import numpy as np
import sys
import os
from pathlib import Path

# Add the backendapp to the path
sys.path.append(str(Path(__file__).parent / "backendapp"))

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
    print("✓ Tesseract is available")
except ImportError:
    TESSERACT_AVAILABLE = False
    print("✗ Tesseract is not available")

def detect_text_orientation_opencv(img):
    """Detect text orientation using OpenCV text detection and line analysis"""
    try:
        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Apply binary threshold
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # Find contours for text regions
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return 0
        
        # Filter contours by area to get text-like regions
        text_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 50 < area < 10000:  # Filter for text-sized regions
                text_contours.append(contour)
        
        if not text_contours:
            return 0
        
        # Calculate orientation angles for text regions
        angles = []
        for contour in text_contours:
            # Get minimum area rectangle
            rect = cv2.minAreaRect(contour)
            angle = rect[2]
            
            # Normalize angle to [-90, 0] range
            if angle > -45:
                angle = angle - 90
            
            angles.append(angle)
        
        if not angles:
            return 0
        
        # Use median angle to avoid outliers
        median_angle = np.median(angles)
        
        print(f"Detected angles: {angles[:10]}...")  # Show first 10 angles
        print(f"Median angle: {median_angle}")
        
        # Round to nearest increment for rotations
        if abs(median_angle) > 75:
            return -90 if median_angle < 0 else 90
        elif abs(median_angle) > 60:
            return -75 if median_angle < 0 else 75
        elif abs(median_angle) > 37.5:
            return -45 if median_angle < 0 else 45
        elif abs(median_angle) > 22.5:
            return -30 if median_angle < 0 else 30
        elif abs(median_angle) > 7.5:
            return -15 if median_angle < 0 else 15
        elif abs(median_angle) > 2:
            return median_angle
        else:
            return 0
            
    except Exception as e:
        print(f"Warning: OpenCV text orientation detection failed: {str(e)}")
        return 0

def test_tesseract_orientation(img):
    """Test Tesseract orientation detection"""
    if not TESSERACT_AVAILABLE:
        print("Tesseract not available, skipping...")
        return img
    
    try:
        # Preprocess image for better OSD detection
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Enhance contrast for better text detection
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # Get orientation and script detection with multiple attempts
        osd = None
        for config in ['--psm 0', '--psm 0 -c min_characters_to_try=5']:
            try:
                print(f"Trying Tesseract with config: {config}")
                osd = pytesseract.image_to_osd(enhanced, config=config)
                print(f"Success with config: {config}")
                break
            except Exception as e:
                print(f"Failed with config {config}: {e}")
                continue
        
        if not osd:
            print("All Tesseract OSD attempts failed")
            return img
        
        print(f"Tesseract OSD output:\n{osd}")
        
        # Extract orientation angle
        import re
        angle_match = re.search(r'Orientation in degrees:\s*(\d+)', osd)
        if angle_match:
            angle = int(angle_match.group(1))
            
            # Extract confidence
            confidence_match = re.search(r'Orientation confidence:\s*(\d+\.?\d*)', osd)
            confidence = float(confidence_match.group(1)) if confidence_match else 0
            
            print(f"Detected angle: {angle}°, confidence: {confidence}")
            
            # Only rotate if confidence is reasonable and angle needs correction
            if confidence > 1.0 and angle != 0:
                print(f"Applying Tesseract rotation: {angle}°")
                if angle == 90:
                    rotated = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
                elif angle == 180:
                    rotated = cv2.rotate(img, cv2.ROTATE_180)
                elif angle == 270:
                    rotated = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
                else:
                    rotated = img
                
                return rotated
            else:
                print(f"Low confidence ({confidence}) or no rotation needed")
                return img
                
    except Exception as e:
        print(f"Warning: Tesseract orientation detection failed: {str(e)}")
    
    return img

def test_opencv_orientation(img):
    """Test OpenCV orientation detection"""
    try:
        # Detect the rotation angle needed
        rotation_angle = detect_text_orientation_opencv(img)
        
        print(f"OpenCV detected rotation angle: {rotation_angle}")
        
        # Only rotate if the angle is significant (lowered threshold for better detection)
        if abs(rotation_angle) > 1.0:
            height, width = img.shape[:2]
            center = (width // 2, height // 2)
            
            # Create rotation matrix
            rotation_matrix = cv2.getRotationMatrix2D(center, -rotation_angle, 1.0)
            
            # Calculate new bounding dimensions to avoid cropping
            abs_cos = abs(rotation_matrix[0,0])
            abs_sin = abs(rotation_matrix[0,1])
            
            new_width = int(height * abs_sin + width * abs_cos)
            new_height = int(height * abs_cos + width * abs_sin)
            
            # Adjust the rotation matrix to account for translation
            rotation_matrix[0, 2] += (new_width - width) / 2
            rotation_matrix[1, 2] += (new_height - height) / 2
            
            rotated = cv2.warpAffine(img, rotation_matrix, (new_width, new_height),
                                   flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_CONSTANT, 
                                   borderValue=(255, 255, 255))
            return rotated
            
    except Exception as e:
        print(f"Warning: OpenCV orientation check failed: {str(e)}")
    
    return img

def main():
    if len(sys.argv) != 2:
        print("Usage: python test_orientation.py <image_path>")
        print("Example: python test_orientation.py media/problematic_image.png")
        sys.exit(1)
    
    image_path = sys.argv[1]
    
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found")
        sys.exit(1)
    
    # Load the image
    img = cv2.imread(image_path)
    if img is None:
        print(f"Error: Could not load image '{image_path}'")
        sys.exit(1)
    
    print(f"Loaded image: {image_path}")
    print(f"Image shape: {img.shape}")
    
    # Test OpenCV method
    print("\n" + "="*50)
    print("TESTING OPENCV ORIENTATION DETECTION")
    print("="*50)
    opencv_result = test_opencv_orientation(img.copy())
    
    # Test Tesseract method
    print("\n" + "="*50)
    print("TESTING TESSERACT ORIENTATION DETECTION")
    print("="*50)
    tesseract_result = test_tesseract_orientation(img.copy())
    
    # Save results
    base_name = Path(image_path).stem
    output_dir = Path("orientation_test_results")
    output_dir.mkdir(exist_ok=True)
    
    cv2.imwrite(str(output_dir / f"{base_name}_original.png"), img)
    cv2.imwrite(str(output_dir / f"{base_name}_opencv_corrected.png"), opencv_result)
    cv2.imwrite(str(output_dir / f"{base_name}_tesseract_corrected.png"), tesseract_result)
    
    print(f"\nResults saved to {output_dir}/")
    print("- {}_original.png (original image)".format(base_name))
    print("- {}_opencv_corrected.png (OpenCV corrected)".format(base_name))
    print("- {}_tesseract_corrected.png (Tesseract corrected)".format(base_name))

if __name__ == "__main__":
    main()
