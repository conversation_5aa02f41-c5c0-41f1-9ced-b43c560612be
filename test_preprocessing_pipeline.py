#!/usr/bin/env python3
"""
Test the full preprocessing pipeline with the improved orientation detection
"""

import cv2
import numpy as np
import fitz  # PyMuPDF
import os
from pathlib import Path

def detect_text_orientation_opencv(img):
    """Detect text orientation using OpenCV text detection and line analysis"""
    try:
        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Apply binary threshold
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # Find contours for text regions
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return 0
        
        # Filter contours by area to get text-like regions
        text_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 50 < area < 10000:  # Filter for text-sized regions
                text_contours.append(contour)
        
        if not text_contours:
            return 0
        
        # Calculate orientation angles for text regions
        angles = []
        for contour in text_contours:
            # Get minimum area rectangle
            rect = cv2.minAreaRect(contour)
            angle = rect[2]
            
            # Normalize angle to [-90, 0] range
            if angle > -45:
                angle = angle - 90
            
            angles.append(angle)
        
        if not angles:
            return 0
        
        # Use median angle to avoid outliers
        median_angle = np.median(angles)
        
        # Round to nearest increment for rotations (IMPROVED)
        if abs(median_angle) > 75:
            return -90 if median_angle < 0 else 90
        elif abs(median_angle) > 60:
            return -75 if median_angle < 0 else 75
        elif abs(median_angle) > 37.5:
            return -45 if median_angle < 0 else 45
        elif abs(median_angle) > 22.5:
            return -30 if median_angle < 0 else 30
        elif abs(median_angle) > 7.5:
            return -15 if median_angle < 0 else 15
        elif abs(median_angle) > 2:
            return median_angle
        else:
            return 0
            
    except Exception as e:
        print(f"Warning: OpenCV text orientation detection failed: {str(e)}")
        return 0

def check_opencv_orientation(img):
    """Improved OpenCV orientation check"""
    try:
        # Detect the rotation angle needed
        rotation_angle = detect_text_orientation_opencv(img)
        
        print(f"OpenCV detected rotation angle: {rotation_angle}")
        
        # Only rotate if the angle is significant (IMPROVED: lowered threshold)
        if abs(rotation_angle) > 1.0:
            height, width = img.shape[:2]
            center = (width // 2, height // 2)
            
            # Create rotation matrix
            rotation_matrix = cv2.getRotationMatrix2D(center, -rotation_angle, 1.0)
            
            # Calculate new bounding dimensions to avoid cropping
            abs_cos = abs(rotation_matrix[0,0])
            abs_sin = abs(rotation_matrix[0,1])
            
            new_width = int(height * abs_sin + width * abs_cos)
            new_height = int(height * abs_cos + width * abs_sin)
            
            # Adjust the rotation matrix to account for translation
            rotation_matrix[0, 2] += (new_width - width) / 2
            rotation_matrix[1, 2] += (new_height - height) / 2
            
            rotated = cv2.warpAffine(img, rotation_matrix, (new_width, new_height),
                                   flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_CONSTANT, 
                                   borderValue=(255, 255, 255))
            return rotated
            
    except Exception as e:
        print(f"Warning: OpenCV orientation check failed: {str(e)}")
    
    return img

def simple_deskew(img, max_skew_angle=15.0):
    """Simple deskewing with conservative parameters"""
    try:
        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
        else:
            gray = img
        
        # Detect edges using Canny
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # Use Hough Line Transform to detect lines
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=150)
        
        if lines is None:
            return img, 0.0
        
        angles = []
        for line in lines:
            rho, theta = line[0]
            angle = np.degrees(theta) - 90  # Convert to degrees from vertical
            
            # Only consider reasonable angles
            if abs(angle) <= max_skew_angle:
                angles.append(angle)
        
        if not angles:
            return img, 0.0
        
        # Use median for robustness against outliers
        skew_angle = np.median(angles)
        
        # Skip rotation if angle is negligible
        if abs(skew_angle) < 0.5:
            return img, skew_angle
        
        # Rotate image using OpenCV
        height, width = img.shape[:2]
        center = (width // 2, height // 2)
        
        # Calculate rotation matrix
        rotation_matrix = cv2.getRotationMatrix2D(center, skew_angle, 1.0)
        
        # Calculate new bounding dimensions
        cos = np.abs(rotation_matrix[0, 0])
        sin = np.abs(rotation_matrix[0, 1])
        new_width = int((height * sin) + (width * cos))
        new_height = int((height * cos) + (width * sin))
        
        # Adjust rotation matrix to prevent cropping
        rotation_matrix[0, 2] += (new_width / 2) - center[0]
        rotation_matrix[1, 2] += (new_height / 2) - center[1]
        
        # Apply rotation
        deskewed = cv2.warpAffine(
            img, 
            rotation_matrix, 
            (new_width, new_height),
            flags=cv2.INTER_CUBIC,
            borderMode=cv2.BORDER_CONSTANT,
            borderValue=(255, 255, 255)
        )
        
        return deskewed, skew_angle
        
    except Exception as e:
        print(f"Error in simple_deskew: {str(e)}")
        return img, 0.0

def process_pdf_page(pdf_path, page_num=0):
    """Process a single page from a PDF"""
    try:
        # Open PDF
        doc = fitz.open(pdf_path)
        page = doc[page_num]
        
        # Convert to image
        mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
        pix = page.get_pixmap(matrix=mat)
        img_data = pix.tobytes("png")
        
        # Convert to OpenCV format
        nparr = np.frombuffer(img_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        print(f"Original image shape: {img.shape}")
        
        # Step 1: Orientation correction
        print("Step 1: Correcting orientation...")
        oriented_img = check_opencv_orientation(img.copy())
        
        # Step 2: Conservative deskewing
        print("Step 2: Deskewing...")
        deskewed_img, skew_angle = simple_deskew(oriented_img, max_skew_angle=15.0)
        print(f"Detected skew angle: {skew_angle:.2f}°")
        
        # Step 3: Convert to grayscale
        print("Step 3: Converting to grayscale...")
        if len(deskewed_img.shape) == 3:
            gray_img = cv2.cvtColor(deskewed_img, cv2.COLOR_RGB2GRAY)
        else:
            gray_img = deskewed_img
        
        doc.close()
        
        return {
            'original': img,
            'oriented': oriented_img,
            'deskewed': deskewed_img,
            'final': gray_img,
            'skew_angle': skew_angle
        }
        
    except Exception as e:
        print(f"Error processing PDF: {str(e)}")
        return None

def main():
    # Test with a PDF file if available
    pdf_files = list(Path("media/answers").glob("*.pdf"))
    
    if pdf_files:
        pdf_path = pdf_files[0]
        print(f"Testing with PDF: {pdf_path}")
        
        result = process_pdf_page(str(pdf_path), page_num=0)
        
        if result:
            # Save results
            output_dir = Path("preprocessing_test_results")
            output_dir.mkdir(exist_ok=True)
            
            base_name = pdf_path.stem
            cv2.imwrite(str(output_dir / f"{base_name}_original.png"), result['original'])
            cv2.imwrite(str(output_dir / f"{base_name}_oriented.png"), result['oriented'])
            cv2.imwrite(str(output_dir / f"{base_name}_deskewed.png"), result['deskewed'])
            cv2.imwrite(str(output_dir / f"{base_name}_final.png"), result['final'])
            
            print(f"\nResults saved to {output_dir}/")
            print(f"- {base_name}_original.png (original)")
            print(f"- {base_name}_oriented.png (after orientation correction)")
            print(f"- {base_name}_deskewed.png (after deskewing)")
            print(f"- {base_name}_final.png (final grayscale)")
            print(f"Detected skew angle: {result['skew_angle']:.2f}°")
        else:
            print("Failed to process PDF")
    else:
        print("No PDF files found in media/answers/")
        print("Testing with the rotated test image instead...")
        
        # Test with our rotated image
        img = cv2.imread('test_rotated_image.png')
        if img is not None:
            print("Step 1: Correcting orientation...")
            oriented_img = check_opencv_orientation(img.copy())
            
            print("Step 2: Deskewing...")
            deskewed_img, skew_angle = simple_deskew(oriented_img, max_skew_angle=15.0)
            
            # Save results
            cv2.imwrite('pipeline_test_oriented.png', oriented_img)
            cv2.imwrite('pipeline_test_final.png', deskewed_img)
            
            print("Results saved:")
            print("- pipeline_test_oriented.png (after orientation correction)")
            print("- pipeline_test_final.png (after deskewing)")
            print(f"Detected skew angle: {skew_angle:.2f}°")

if __name__ == "__main__":
    main()
