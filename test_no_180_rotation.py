#!/usr/bin/env python3
"""
Test that 180° rotations are now disabled
"""

import cv2
import numpy as np

def simulate_tesseract_result(rotate_angle, confidence):
    """Simulate what the fixed Tesseract logic would do"""
    print(f"\n=== Simulating Tesseract: Rotate={rotate_angle}°, Confidence={confidence} ===")
    
    # This is the logic from the fixed ensure_orientation function
    if confidence > 1.0 and rotate_angle != 0:
        print(f"Tesseract suggests {rotate_angle}° rotation with confidence {confidence}")
        
        # TEMPORARY FIX: Disable 180° rotations entirely
        if rotate_angle == 180:
            print(f"SKIPPING 180° rotation - known to cause issues with properly oriented documents")
            print("Keeping original orientation")
            return "NO_ROTATION"
        
        # Handle 90° and 270° rotations only
        print(f"Applying Tesseract rotation: {rotate_angle}°")
        if rotate_angle == 90:
            return "ROTATE_90_CLOCKWISE"
        elif rotate_angle == 270:
            return "ROTATE_90_COUNTERCLOCKWISE"
        else:
            print(f"Unsupported rotation angle: {rotate_angle}°, keeping original")
            return "NO_ROTATION"
    else:
        print(f"Low confidence ({confidence}) or no rotation needed")
        if rotate_angle == 0 and confidence > 0.5:
            print("Tesseract indicates no rotation needed - skipping OpenCV fallback")
            return "NO_ROTATION"
        else:
            print("Using OpenCV fallback")
            return "OPENCV_FALLBACK"

def main():
    print("="*60)
    print("TESTING FIXED ROTATION LOGIC")
    print("="*60)
    
    # Test scenarios based on what we observed
    test_cases = [
        # (rotate_angle, confidence, description)
        (0, 1.3, "Page 1 - No rotation needed"),
        (180, 1.5, "Page 2 - Previously caused inversion"),
        (180, 2.5, "Page 3 - Previously caused inversion (higher confidence)"),
        (90, 2.0, "90° rotation test"),
        (270, 2.0, "270° rotation test"),
        (0, 0.3, "Low confidence, no rotation"),
    ]
    
    for rotate_angle, confidence, description in test_cases:
        print(f"\nTest Case: {description}")
        result = simulate_tesseract_result(rotate_angle, confidence)
        print(f"Result: {result}")
        
        if rotate_angle == 180:
            if result == "NO_ROTATION":
                print("✅ SUCCESS: 180° rotation was correctly skipped")
            else:
                print("❌ FAILED: 180° rotation was not skipped")
        elif rotate_angle == 0:
            if result == "NO_ROTATION":
                print("✅ SUCCESS: No rotation applied as expected")
            else:
                print("⚠️  WARNING: Unexpected result for 0° case")
    
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    print("The fix should now:")
    print("✅ Keep Page 1 correct (0° rotation)")
    print("✅ Prevent Pages 2 & 3 from being inverted (skip 180° rotations)")
    print("✅ Still allow 90° and 270° rotations when truly needed")
    print("\nProcess your PDF again to test the fix!")

if __name__ == "__main__":
    main()
