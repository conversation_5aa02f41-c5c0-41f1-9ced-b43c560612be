#!/usr/bin/env python3
"""
Diagnostic script to identify where rotation is being introduced in the preprocessing pipeline
"""

import cv2
import numpy as np
import fitz  # PyMuPDF
import sys
from pathlib import Path

def save_debug_image(img, filename, step_name):
    """Save debug image with step information"""
    cv2.imwrite(filename, img)
    print(f"✓ Saved {filename} - {step_name}")
    print(f"  Shape: {img.shape}")
    if len(img.shape) == 3:
        print(f"  Channels: {img.shape[2]}")

def test_pdf_extraction(pdf_path):
    """Test PDF extraction without any processing"""
    try:
        print(f"Testing PDF extraction from: {pdf_path}")
        doc = fitz.open(pdf_path)
        
        print(f"PDF has {len(doc)} pages")
        
        # Test different extraction methods
        for page_num in range(min(3, len(doc))):  # Test first 3 pages
            page = doc[page_num]
            print(f"\n=== PAGE {page_num + 1} ===")
            
            # Method 1: Default extraction
            print("Method 1: Default extraction (1x zoom)")
            mat1 = fitz.Matrix(1.0, 1.0)
            pix1 = page.get_pixmap(matrix=mat1)
            img_data1 = pix1.tobytes("png")
            nparr1 = np.frombuffer(img_data1, np.uint8)
            img1 = cv2.imdecode(nparr1, cv2.IMREAD_COLOR)
            save_debug_image(img1, f"debug_page{page_num+1}_method1_1x.png", "Default 1x zoom")
            
            # Method 2: 2x zoom extraction
            print("Method 2: 2x zoom extraction")
            mat2 = fitz.Matrix(2.0, 2.0)
            pix2 = page.get_pixmap(matrix=mat2)
            img_data2 = pix2.tobytes("png")
            nparr2 = np.frombuffer(img_data2, np.uint8)
            img2 = cv2.imdecode(nparr2, cv2.IMREAD_COLOR)
            save_debug_image(img2, f"debug_page{page_num+1}_method2_2x.png", "2x zoom")
            
            # Method 3: Different color space
            print("Method 3: RGB color space")
            pix3 = page.get_pixmap(matrix=mat2, colorspace=fitz.csRGB)
            img_data3 = pix3.tobytes("png")
            nparr3 = np.frombuffer(img_data3, np.uint8)
            img3 = cv2.imdecode(nparr3, cv2.IMREAD_COLOR)
            save_debug_image(img3, f"debug_page{page_num+1}_method3_rgb.png", "RGB colorspace")
            
            # Check if images are identical
            if np.array_equal(img2, img3):
                print("  ✓ Method 2 and 3 produce identical results")
            else:
                print("  ⚠ Method 2 and 3 produce different results")
            
            # Test basic image properties
            print(f"  Image dimensions: {img2.shape}")
            print(f"  Data type: {img2.dtype}")
            print(f"  Min/Max values: {img2.min()}/{img2.max()}")
            
            # Test if image appears rotated by checking text orientation
            gray = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
            
            # Simple rotation detection
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            if lines is not None:
                angles = []
                for line in lines[:10]:  # Check first 10 lines
                    rho, theta = line[0]
                    angle = np.degrees(theta) - 90
                    angles.append(angle)
                
                if angles:
                    median_angle = np.median(angles)
                    print(f"  Detected text orientation: {median_angle:.1f}°")
                    if abs(median_angle) > 10:
                        print(f"  ⚠ WARNING: Text appears rotated by {median_angle:.1f}°")
                    else:
                        print(f"  ✓ Text appears properly oriented")
                else:
                    print("  ? Could not detect text orientation")
            else:
                print("  ? No lines detected for orientation analysis")
        
        doc.close()
        return True
        
    except Exception as e:
        print(f"Error testing PDF extraction: {str(e)}")
        return False

def test_orientation_detection_on_good_image(img):
    """Test orientation detection on a properly oriented image"""
    print("\n=== TESTING ORIENTATION DETECTION ON GOOD IMAGE ===")
    
    # Test Tesseract if available
    try:
        import pytesseract
        print("Testing Tesseract orientation detection...")
        
        # Preprocess for Tesseract
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        try:
            osd = pytesseract.image_to_osd(enhanced, config='--psm 0')
            print("Tesseract OSD output:")
            print(osd)
            
            # Parse results
            import re
            angle_match = re.search(r'Orientation in degrees:\s*(\d+)', osd)
            confidence_match = re.search(r'Orientation confidence:\s*(\d+\.?\d*)', osd)
            
            if angle_match and confidence_match:
                angle = int(angle_match.group(1))
                confidence = float(confidence_match.group(1))
                print(f"Detected: {angle}° rotation with {confidence} confidence")
                
                if angle == 0:
                    print("✓ Tesseract correctly detects no rotation needed")
                else:
                    print(f"⚠ Tesseract incorrectly detects {angle}° rotation needed")
            
        except Exception as e:
            print(f"Tesseract OSD failed: {e}")
            
    except ImportError:
        print("Tesseract not available")
    
    # Test OpenCV method
    print("\nTesting OpenCV orientation detection...")
    
    # Convert to grayscale if needed
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        gray = img.copy()
    
    # Apply binary threshold
    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    
    # Find contours for text regions
    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        # Filter contours by area
        text_contours = [c for c in contours if 50 < cv2.contourArea(c) < 10000]
        
        if text_contours:
            angles = []
            for contour in text_contours[:20]:  # Check first 20 contours
                rect = cv2.minAreaRect(contour)
                angle = rect[2]
                if angle > -45:
                    angle = angle - 90
                angles.append(angle)
            
            if angles:
                median_angle = np.median(angles)
                print(f"OpenCV detected median angle: {median_angle:.1f}°")
                
                if abs(median_angle) < 5:
                    print("✓ OpenCV correctly detects minimal rotation")
                else:
                    print(f"⚠ OpenCV detects {median_angle:.1f}° rotation")
            else:
                print("No angles detected")
        else:
            print("No text-like contours found")
    else:
        print("No contours found")

def main():
    if len(sys.argv) != 2:
        print("Usage: python diagnose_rotation_issue.py <pdf_path>")
        print("Example: python diagnose_rotation_issue.py backendapp/uploads/4.1_13.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not Path(pdf_path).exists():
        print(f"Error: PDF file '{pdf_path}' not found")
        sys.exit(1)
    
    print("="*60)
    print("DIAGNOSTIC TEST FOR ROTATION ISSUE")
    print("="*60)
    
    # Test PDF extraction
    if test_pdf_extraction(pdf_path):
        print("\n✓ PDF extraction completed successfully")
        
        # Load the first extracted image for orientation testing
        debug_files = list(Path(".").glob("debug_page1_method2_2x.png"))
        if debug_files:
            img = cv2.imread(str(debug_files[0]))
            if img is not None:
                test_orientation_detection_on_good_image(img)
            else:
                print("Could not load extracted image for orientation testing")
        else:
            print("No debug images found for orientation testing")
    else:
        print("✗ PDF extraction failed")
    
    print("\n" + "="*60)
    print("DIAGNOSIS COMPLETE")
    print("="*60)
    print("Check the debug_page*.png files to see the extracted images")
    print("If they appear rotated, the issue is in PDF extraction")
    print("If they appear correct, the issue is in the preprocessing pipeline")

if __name__ == "__main__":
    main()
