#!/usr/bin/env python3
"""
Debug orientation detection for each page separately
"""

import cv2
import numpy as np
import fitz  # PyMuPDF
import sys
from pathlib import Path

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
    print("✓ Tesseract is available")
except ImportError:
    TESSERACT_AVAILABLE = False
    print("✗ Tesseract is not available")

def test_tesseract_on_page(img, page_num):
    """Test Tesseract orientation detection on a specific page"""
    if not TESSERACT_AVAILABLE:
        return None
    
    try:
        # Preprocess image for better OSD detection
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Enhance contrast for better text detection
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        print(f"\n=== PAGE {page_num} TESSERACT ANALYSIS ===")
        
        # Get orientation and script detection with multiple attempts
        osd = None
        for config in ['--psm 0', '--psm 0 -c min_characters_to_try=5']:
            try:
                print(f"Trying Tesseract with config: {config}")
                osd = pytesseract.image_to_osd(enhanced, config=config)
                print(f"Success with config: {config}")
                break
            except Exception as e:
                print(f"Failed with config {config}: {e}")
                continue
        
        if not osd:
            print("All Tesseract OSD attempts failed")
            return None
        
        print(f"Tesseract OSD output:\n{osd}")
        
        # Extract orientation angle
        import re
        angle_match = re.search(r'Orientation in degrees:\s*(\d+)', osd)
        rotate_match = re.search(r'Rotate:\s*(\d+)', osd)
        confidence_match = re.search(r'Orientation confidence:\s*(\d+\.?\d*)', osd)
        
        if angle_match and confidence_match:
            angle = int(angle_match.group(1))
            rotate = int(rotate_match.group(1)) if rotate_match else angle
            confidence = float(confidence_match.group(1))
            
            print(f"Detected:")
            print(f"  - Orientation in degrees: {angle}°")
            print(f"  - Rotate: {rotate}°")
            print(f"  - Confidence: {confidence}")
            
            return {
                'angle': angle,
                'rotate': rotate,
                'confidence': confidence,
                'osd': osd
            }
        
        return None
        
    except Exception as e:
        print(f"Error in Tesseract analysis: {e}")
        return None

def apply_rotation_test(img, rotation_degrees, method_name):
    """Apply rotation and save result for testing"""
    if rotation_degrees == 0:
        print(f"{method_name}: No rotation needed")
        return img
    
    print(f"{method_name}: Applying {rotation_degrees}° rotation")
    
    if rotation_degrees == 90:
        rotated = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
    elif rotation_degrees == 180:
        rotated = cv2.rotate(img, cv2.ROTATE_180)
    elif rotation_degrees == 270:
        rotated = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
    else:
        # For other angles, use affine transformation
        height, width = img.shape[:2]
        center = (width // 2, height // 2)
        rotation_matrix = cv2.getRotationMatrix2D(center, rotation_degrees, 1.0)
        rotated = cv2.warpAffine(img, rotation_matrix, (width, height), 
                               borderMode=cv2.BORDER_CONSTANT, borderValue=(255, 255, 255))
    
    return rotated

def main():
    pdf_path = "backendapp/uploads/17/4_1_13.pdf"
    
    if not Path(pdf_path).exists():
        print(f"Error: PDF file '{pdf_path}' not found")
        return
    
    try:
        print("="*60)
        print("PER-PAGE ORIENTATION ANALYSIS")
        print("="*60)
        
        doc = fitz.open(pdf_path)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Extract page as image
            mat = fitz.Matrix(2.0, 2.0)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            nparr = np.frombuffer(img_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            print(f"\n{'='*20} PAGE {page_num + 1} {'='*20}")
            print(f"Image shape: {img.shape}")
            
            # Save original
            cv2.imwrite(f"page{page_num+1}_original.png", img)
            
            # Test Tesseract orientation detection
            tesseract_result = test_tesseract_on_page(img, page_num + 1)
            
            if tesseract_result:
                # Test different rotation interpretations
                angle = tesseract_result['angle']
                rotate = tesseract_result['rotate']
                confidence = tesseract_result['confidence']
                
                print(f"\nTesting different rotation interpretations:")
                
                # Method 1: Use the "Orientation in degrees" value directly
                result1 = apply_rotation_test(img.copy(), angle, "Method 1 (Orientation)")
                cv2.imwrite(f"page{page_num+1}_method1_orient{angle}.png", result1)
                
                # Method 2: Use the "Rotate" value directly
                result2 = apply_rotation_test(img.copy(), rotate, "Method 2 (Rotate)")
                cv2.imwrite(f"page{page_num+1}_method2_rotate{rotate}.png", result2)
                
                # Method 3: Invert the rotation (opposite direction)
                inverted_rotate = (360 - rotate) % 360 if rotate != 0 else 0
                result3 = apply_rotation_test(img.copy(), inverted_rotate, f"Method 3 (Inverted)")
                cv2.imwrite(f"page{page_num+1}_method3_inverted{inverted_rotate}.png", result3)
                
                # Method 4: Current preprocessing logic
                if confidence > 1.0 and rotate != 0:
                    if rotate == 90:
                        result4 = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
                    elif rotate == 180:
                        result4 = cv2.rotate(img, cv2.ROTATE_180)
                    elif rotate == 270:
                        result4 = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
                    else:
                        result4 = img
                    
                    cv2.imwrite(f"page{page_num+1}_method4_current.png", result4)
                    print(f"Method 4 (Current): Applied {rotate}° rotation")
                else:
                    cv2.imwrite(f"page{page_num+1}_method4_current.png", img)
                    print(f"Method 4 (Current): No rotation (confidence {confidence})")
                
                print(f"\nSaved test results for page {page_num + 1}:")
                print(f"  - page{page_num+1}_original.png")
                print(f"  - page{page_num+1}_method1_orient{angle}.png")
                print(f"  - page{page_num+1}_method2_rotate{rotate}.png")
                print(f"  - page{page_num+1}_method3_inverted{inverted_rotate}.png")
                print(f"  - page{page_num+1}_method4_current.png")
            else:
                print("Tesseract analysis failed for this page")
                cv2.imwrite(f"page{page_num+1}_tesseract_failed.png", img)
        
        doc.close()
        
        print("\n" + "="*60)
        print("ANALYSIS COMPLETE")
        print("="*60)
        print("Check the generated images to see which method produces correctly oriented pages")
        print("Compare with the original PDF to determine the correct approach")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
