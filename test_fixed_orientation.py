#!/usr/bin/env python3
"""
Test the fixed orientation detection
"""

import cv2
import numpy as np
import sys
import os
from pathlib import Path

# Add the backendapp to the path
sys.path.append(str(Path(__file__).parent / "backendapp"))

def detect_text_orientation_opencv_fixed(img):
    """Fixed version of text orientation detection - much more conservative"""
    try:
        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()

        # Use Hough Line Transform instead of contour analysis for better accuracy
        # Apply edge detection
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)

        # Use Hough Line Transform to detect lines
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

        if lines is None:
            print("No lines detected for orientation analysis")
            return 0

        # Analyze line orientations
        angles = []
        for line in lines:
            rho, theta = line[0]
            # Convert theta to degrees and normalize to text orientation
            angle_deg = np.degrees(theta)

            # Convert to text baseline angle (horizontal = 0°)
            # Vertical lines (90° and 270°) become 0° (horizontal text)
            # Horizontal lines (0° and 180°) become 90° (vertical text)
            if 45 <= angle_deg <= 135:  # Roughly vertical lines (normal horizontal text)
                text_angle = 0
            elif angle_deg < 45 or angle_deg > 135:  # Roughly horizontal lines (rotated text)
                if angle_deg < 45:
                    text_angle = 90 - angle_deg
                else:
                    text_angle = angle_deg - 90
            else:
                continue  # Skip ambiguous angles

            # Only consider significant rotations
            if abs(text_angle) > 5:
                angles.append(text_angle)

        if not angles:
            print("No significant text rotation detected")
            return 0

        # Use median angle to avoid outliers
        median_angle = np.median(angles)
        print(f"Detected line angles: {angles[:10]}... (showing first 10)")
        print(f"Median text rotation angle: {median_angle:.1f}°")

        # Only return rotation if it's significant and consistent (MUCH MORE STRICT)
        if len(angles) < 10:  # Need at least 10 consistent measurements
            print("Insufficient consistent measurements for rotation detection")
            return 0

        # Check for consistency - if angles are too spread out, don't trust the result
        angle_std = np.std(angles)
        if angle_std > 5:  # Much stricter variance requirement
            print(f"High angle variance ({angle_std:.1f}°), skipping rotation")
            return 0

        # Round to reasonable increments
        if abs(median_angle) > 75:
            return -90 if median_angle < 0 else 90
        elif abs(median_angle) > 60:
            return -75 if median_angle < 0 else 75
        elif abs(median_angle) > 37.5:
            return -45 if median_angle < 0 else 45
        elif abs(median_angle) > 22.5:
            return -30 if median_angle < 0 else 30
        elif abs(median_angle) > 7.5:
            return -15 if median_angle < 0 else 15
        elif abs(median_angle) > 10:  # Much higher threshold to avoid false positives
            return median_angle
        else:
            print(f"Rotation angle {median_angle:.1f}° is below threshold, no rotation applied")
            return 0
            
    except Exception as e:
        print(f"Warning: OpenCV text orientation detection failed: {str(e)}")
        return 0

def check_opencv_orientation_fixed(img):
    """Fixed OpenCV orientation check with higher thresholds"""
    try:
        # Detect the rotation angle needed
        rotation_angle = detect_text_orientation_opencv_fixed(img)
        
        print(f"OpenCV detected rotation angle: {rotation_angle}")
        
        # Only rotate if the angle is significant (increased threshold to avoid false positives)
        if abs(rotation_angle) > 5.0:
            height, width = img.shape[:2]
            center = (width // 2, height // 2)
            
            # Create rotation matrix
            rotation_matrix = cv2.getRotationMatrix2D(center, -rotation_angle, 1.0)
            
            # Calculate new bounding dimensions to avoid cropping
            abs_cos = abs(rotation_matrix[0,0])
            abs_sin = abs(rotation_matrix[0,1])
            
            new_width = int(height * abs_sin + width * abs_cos)
            new_height = int(height * abs_cos + width * abs_sin)
            
            # Adjust the rotation matrix to account for translation
            rotation_matrix[0, 2] += (new_width - width) / 2
            rotation_matrix[1, 2] += (new_height - height) / 2
            
            rotated = cv2.warpAffine(img, rotation_matrix, (new_width, new_height),
                                   flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_CONSTANT, 
                                   borderValue=(255, 255, 255))
            return rotated
        else:
            print(f"Rotation angle {rotation_angle}° is below threshold (5°), no rotation applied")
            
    except Exception as e:
        print(f"Warning: OpenCV orientation check failed: {str(e)}")
    
    return img

def main():
    # Test with the debug image from the properly oriented PDF
    debug_image = "debug_page2_method2_2x.png"
    
    if not os.path.exists(debug_image):
        print(f"Debug image {debug_image} not found. Run the diagnostic script first.")
        return
    
    img = cv2.imread(debug_image)
    if img is None:
        print(f"Could not load {debug_image}")
        return
    
    print(f"Testing fixed orientation detection on {debug_image}")
    print(f"Original image shape: {img.shape}")
    
    # Test the fixed orientation detection
    result = check_opencv_orientation_fixed(img.copy())
    
    # Check if any rotation was applied
    if np.array_equal(img, result):
        print("\n✅ SUCCESS: No unwanted rotation was applied!")
        print("The fixed algorithm correctly identified that no rotation is needed.")
    else:
        print("\n❌ ISSUE: Rotation was still applied")
        print(f"Result image shape: {result.shape}")
        cv2.imwrite('fixed_test_result.png', result)
        print("Saved fixed_test_result.png for inspection")
    
    # Also test with the original rotated test image to make sure we can still detect real rotations
    if os.path.exists('test_rotated_image.png'):
        print(f"\n--- Testing with intentionally rotated image ---")
        rotated_img = cv2.imread('test_rotated_image.png')
        if rotated_img is not None:
            rotated_result = check_opencv_orientation_fixed(rotated_img.copy())
            
            if not np.array_equal(rotated_img, rotated_result):
                print("✅ Good: Still able to detect and correct intentional rotations")
                cv2.imwrite('fixed_rotated_test_result.png', rotated_result)
            else:
                print("⚠️  Warning: May have become too conservative - not detecting intentional rotations")

if __name__ == "__main__":
    main()
